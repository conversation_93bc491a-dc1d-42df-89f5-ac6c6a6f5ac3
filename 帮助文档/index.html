<!doctype html>  
<html lang="en-us">
<head>
	<meta charset="utf-8">
	
	<title>Medicat - Medical and Health HTML5 Template </title>
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	
	<meta name="description" content="Medicat is a HTML Template specially designed for Health, Medical, Hospitals, Medical Businesses, Professional Services, and Health care, and Pharmacy services. The Medical template has a beautiful and unique design that will be best suited for your online web presence">
 
	
	<link rel="shortcut icon" type="image/x-icon" href="assets/images/favicon.png" />
	<link rel="stylesheet" href="assets/css/documenter_style.css" media="all">
	<link rel="stylesheet" href="assets/js/google-code-prettify/prettify.css" media="screen">
	<script src="assets/js/google-code-prettify/prettify.js"></script>

	<script src="assets/js/jquery.js"></script>
	
	<script src="assets/js/jquery.scrollTo.js"></script>
	<script src="assets/js/jquery.easing.js"></script>
	<!-- <script src="assets/js/waypoints.min.js"></script> -->
	
	<script>document.createElement('section');var duration='500',easing='swing';</script>
	<script src="assets/js/script.js"></script>
	
	<style>
		html{background-color:#EEEEEE;color:#383838;}
		::-moz-selection{background:#333636;color:#ebaa02;}
		::selection{background:#333636;color:#ebaa02;}
		#documenter_sidebar #documenter_logo{background-image:url(assets/images/logo.png);}
		a{color:#000; font-weight: 700;}
		.btn {
			border-radius:3px;
		}
		.btn-primary {
		  	background-image: -moz-linear-gradient(top, #008C9E, #006673);
			background-image: -ms-linear-gradient(top, #008C9E, #006673);
			background-image: -webkit-gradient(linear, 0 0, 0 008C9E%, from(#343838), to(#006673));
			background-image: -webkit-linear-gradient(top, #008C9E, #006673);
			background-image: -o-linear-gradient(top, #008C9E, #006673);
			background-image: linear-gradient(top, #008C9E, #006673);
			filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#008C9E', endColorstr='#006673', GradientType=0);
			border-color: #006673 #006673 #bfbfbf;
			color:#FFFFFF;
		}
		.btn-primary:hover,
		.btn-primary:active,
		.btn-primary.active,
		.btn-primary.disabled,
		.btn-primary[disabled] {
		  border-color: #008C9E #008C9E #bfbfbf;
		  background-color: #006673;
		}
		hr{border-top:1px solid #D4D4D4;border-bottom:1px solid #FFFFFF;}
		#documenter_sidebar, #documenter_sidebar ul a {
		    background-color: #fff;
		    color: #FFFFFF;
		}
		#documenter_sidebar ul a {
		    color: #3a3850;
		}
		#documenter_sidebar ul a:hover{background:#fff;color:#ff4f58;}
		#documenter_sidebar ul li.current a{background:#fff;color:#ff4f58;}
		#documenter_copyright{display:block !important;visibility:visible !important; padding-right: 51px; color: #3a3850;}
	</style>
	
</head>
<body class="documenter-project-demand">
	<div class="main_document">
		<div id="documenter_sidebar">
			<div class="documenter_sidebar_fixed">
				<a href="#documenter_cover" id="documenter_logo"></a>
				<ul id="documenter_nav">
					<li class="current"><a href="#documenter_cover">Start</a></li>
					<li><a href="#installation" title="Installation">Installation</a></li>
					<li><a href="#features_at_a_glance" title="Features At A Glance">Features At A Glance</a></li>
					<li><a href="#html_structure" title="HTML Structure">HTML Structure</a></li>
					<li><a href="#css_files_and_structure" title="CSS Files and Structure">CSS Structure</a></li>
					<li><a href="#js_files_and_structure" title="CSS Files and Structure">Js Structure</a></li>
					<li><a href="#customization" title="customization">Customization</a></li>
					<li><a href="#credits" title="credits">credits</a></li>
				</ul>
				<div id="documenter_copyright">Copyright themerunch 2020</div>
			</div>
		</div>

		<div id="documenter_content">
			<div class="documenter_content_area">
				<section id="documenter_cover">
					<h1>Medicat</h1>
					<h2>Medicat - Medical and Health HTML5 Template </h2>
					<div id="documenter_buttons">
						<a href="http://www.bootstrapmb.com" class="btn theme-color btn-large" target="_blank">Demo</a> <a href="mailto:<EMAIL>" class="btn btn-large">Support</a> 
					</div>
					<hr>
					<ul>
						<li>Created: 17/09/2020</li>
						<li>Latest Update: 29/09/2020</li>
					 
					</ul>
				</section>
				
				<section id="installation">
					<div class="page-header"><h3>Installation</h3><hr class="notop"></div>
					<p style="box-sizing: border-box; margin: 0px 0px 20px; line-height: 1.7; color: rgb(68, 68, 68); font-family: 'Roboto', sans-serif; font-size: 15px;">Follow the steps below to get started with Medicat - Medical and Health HTML5 Template &nbsp;:</p>

					<ol style="box-sizing: border-box; margin-top: 0px; margin-bottom: 20px; color: rgb(68, 68, 68); font-family: 'Roboto', sans-serif; font-size: 15px; line-height: 21px;">
						<li style="box-sizing: border-box;">
							Open the&nbsp;<code style="box-sizing: border-box; font-family: Menlo, Monaco, Consolas, 'Courier New', monospace; font-size: 13px; padding: 2px 4px; color: #fff; border-radius: 4px; background-color: #23cc88;">Package/Medicat</code>&nbsp;Folder to find all the Templates Files
						</li>
						<li style="box-sizing: border-box;">
							You will need to Upload these files to your Web Server using FTP or cPanle in order to use it on your Website.
						</li>
						<li style="box-sizing: border-box;">
							Make sure you have upload the required files and folders listed below:
							<ul style="box-sizing: border-box; margin-top: 0px; margin-bottom: 20px;">
								<li style="box-sizing: border-box;">
									<code style="box-sizing: border-box; font-family: Menlo, Monaco, Consolas, 'Courier New', monospace; font-size: 13px; padding: 2px 4px; color: #fff; border-radius: 4px; background-color: #23cc88;">Medicat HTML/css, js, fonts</code>&nbsp;- Stylesheets, JavaScript, Fonts etc Folder&nbsp;
								</li>
								<li style="box-sizing: border-box;">
									<code style="box-sizing: border-box; font-family: Menlo, Monaco, Consolas, 'Courier New', monospace; font-size: 13px; padding: 2px 4px; color: #fff; border-radius: 4px; background-color: #23cc88;">Medicat HTML/img</code>&nbsp;- Images Folder &nbsp;
								</li>
								<li style="box-sizing: border-box;">
									<code style="box-sizing: border-box; font-family: Menlo, Monaco, Consolas, 'Courier New', monospace; font-size: 13px; padding: 2px 4px; color: #fff; border-radius: 4px; background-color: #23cc88;">Medicat HTML/index.html</code>&nbsp;- Index File/Homepage
								</li>
							</ul>
							The other files can be used according to your preferences.
						</li>
						<li style="box-sizing: border-box;">You&#39;re now ready to go..! Start adding your Content and show off your Brand New Website.</li>
					</ol>
				</section>

				<section id="features_at_a_glance">
					<div class="page-header"><h3>Features At A Glance</h3><hr class="notop"></div>
					<ol>
						<li>HTML5 & CSS3 & jQuery</li>
						<li>Modern And Creative Design</li>
						<li>Universal menu</li>
						<li>100% Responsive</li>
						<li>Well documented</li>
						<li>Bootstrap 4.x</li>
						<li>Fontawesome Used</li>
						<li>Google Font Used</li>
						<li>Fixed Menu</li>
						<li>Google Map</li>
						<li>Eye Catching Design</li>
						<li>W3C Validate Code</li>
					</ol>
				</section>

				<section id="html_structure">
					<div class="page-header"><h3>HTML Structure</h3><hr class="notop"></div>
					<p>
						<span style="color: rgb(68, 68, 68); font-family: 'Roboto', sans-serif; font-size: 15px; line-height: 23.7999992370605px;">Medicat has follows a simple coding structure. here is the sample:</span>
					</p>
					<p>&nbsp;</p>

					<pre class="prettyprint lang-html linenums">
						<div>
&lt;!doctype html&gt;
&lt;html class="no-js" lang="zxx"&gt;
    &lt;head&gt;
        &lt;meta charset="utf-8"&gt;
        &lt;meta http-equiv="x-ua-compatible" content="ie=edge"&gt;
        &lt;title&gt;Medicat - Medical and Health HTML5 Template&lt;/title&gt;
        &lt;meta name="description" content=""&gt;
        &lt;meta name="viewport" content="width=device-width, initial-scale=1"&gt;

        &lt;link rel="manifest" href="site.webmanifest"&gt;
		&lt;link rel="shortcut icon" type="image/x-icon" href="img/favicon.png"&gt;
        &lt;!-- Place favicon.png in the root directory --&gt;

        &lt;!-- CSS here --&gt;
        &lt;link rel="stylesheet" href="css/bootstrap.min.css"&gt;
        &lt;link rel="stylesheet" href="css/owl.carousel.min.css"&gt;
        &lt;link rel="stylesheet" href="css/fontawesome-all.min.css"&gt;
        &lt;link rel="stylesheet" href="css/animate.css"&gt;
        &lt;link rel="stylesheet" href="css/magnific-popup.css"&gt;
        &lt;link rel="stylesheet" href="css/meanmenu.css"&gt;
        &lt;link rel="stylesheet" href="css/nice-select.css"&gt;
        &lt;link rel="stylesheet" href="css/slick.css"&gt;
        &lt;link rel="stylesheet" href="css/default.css"&gt;
        &lt;link rel="stylesheet" href="css/style.css"&gt;
        &lt;link rel="stylesheet" href="css/responsive.css"&gt;
    &lt;/head&gt;
    &lt;body&gt;

	&lt;!-- preloader start --&gt;
        &lt;div id="preloader"&gt;
		&lt;div class="preloader"&gt;
			&lt;span&gt;&lt;/span&gt;
			&lt;span&gt;&lt;/span&gt;
            &lt;/div&gt;
        &lt;/div&gt;
        &lt;!-- preloader end  --&gt;

        &lt;!-- header start --&gt;
        &lt;header&gt;
            &lt;div class="header-top d-none d-lg-block"&gt;
                &lt;div class="container"&gt;
                    &lt;div class="row"&gt;
                        &lt;div class="col-12"&gt;
                            &lt;ul class="left"&gt;
                                &lt;li&gt;&lt;span&gt;&lt;i class="fas fa-phone-alt"&gt;&lt;/i&gt;&lt;/span&gt;+1255 - 568 - 6523&lt;/li&gt;
                                &lt;li&gt;&lt;span&gt;&lt;i class="fas fa-envelope-open-text"&gt;&lt;/i&gt;&lt;/span&gt;<EMAIL>&lt;/li&gt;
                                &lt;li&gt;&lt;span&gt;&lt;i class="fas fa-map-marker-alt"&gt;&lt;/i&gt;&lt;/span&gt;Bowery St., New York, NY 10013, USA&lt;/li&gt;
                            &lt;/ul&gt;
                            &lt;ul class="right"&gt;
                                &lt;li&gt;&lt;a href="#"&gt;&lt;i class="fab fa-facebook-f"&gt;&lt;/i&gt;&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a href="#"&gt;&lt;i class="fab fa-twitter"&gt;&lt;/i&gt;&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a href="#"&gt;&lt;i class="fab fa-vimeo-v"&gt;&lt;/i&gt;&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a href="#"&gt;&lt;i class="fab fa-skype"&gt;&lt;/i&gt;&lt;/a&gt;&lt;/li&gt;
                                &lt;li&gt;&lt;a href="#"&gt;&lt;i class="fas fa-rss"&gt;&lt;/i&gt;&lt;/a&gt;&lt;/li&gt;
                            &lt;/ul&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="header-bottom"&gt;
                &lt;div class="container"&gt;
                    &lt;div class="row align-items-center"&gt;
                        &lt;div class="col-xl-3 col-lg-2"&gt;
                            &lt;div class="logo"&gt;
                                &lt;a href="index.html"&gt;&lt;img src="img/logo/logo.png" alt="logo"&gt;&lt;/a&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-xl-9 col-lg-10"&gt;
                            &lt;div class="bar header-icon d-none d-lg-block f-right"&gt;
                                &lt;a href="#"&gt;&lt;i class="fas fa-th"&gt;&lt;/i&gt;&lt;/a&gt;
                            &lt;/div&gt;
                            &lt;div class="main-menu f-right"&gt;
                                &lt;nav id="mobile-menu"&gt;
                                    &lt;ul&gt;
                                        &lt;li class="dropdown"&gt;&lt;a href="index.html"&gt;Home&lt;/a&gt;
                                            &lt;ul class="submenu"&gt;
                                                &lt;li&gt;&lt;a href="index.html"&gt;Home style 1&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="index-2.html"&gt;Home style 2&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="index-3.html"&gt;Home style 3&lt;/a&gt;&lt;/li&gt;
                                            &lt;/ul&gt;
                                        &lt;/li&gt;
                                        &lt;li class="dropdown"&gt;&lt;a href="services.html"&gt;Department&lt;/a&gt;
                                            &lt;ul class="submenu"&gt;
                                                &lt;li&gt;&lt;a href="services.html"&gt;Services&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="services-details.html"&gt;Services Details&lt;/a&gt;&lt;/li&gt;
                                            &lt;/ul&gt;
                                        &lt;/li&gt;
                                        &lt;li class="dropdown"&gt;&lt;a href="team.html"&gt;Doctors&lt;/a&gt;
                                            &lt;ul class="submenu"&gt;
                                                &lt;li&gt;&lt;a href="team.html"&gt;Doctors&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="team-details.html"&gt;Doctors Details&lt;/a&gt;&lt;/li&gt;
                                            &lt;/ul&gt;
                                        &lt;/li&gt;
                                        &lt;li class="dropdown"&gt;&lt;a href="shop.html"&gt;Shop&lt;/a&gt;
                                            &lt;ul class="submenu"&gt;
                                                &lt;li&gt;&lt;a href="shop.html"&gt;Shop&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="product-details.html"&gt;Shop Details&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="cart.html"&gt;Shopping Cart&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="login.html"&gt;Login&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="register.html"&gt;Register&lt;/a&gt;&lt;/li&gt;
                                            &lt;/ul&gt;
                                        &lt;/li&gt;
                                        &lt;li class="dropdown"&gt;&lt;a href="blog.html"&gt;News&lt;/a&gt;
                                            &lt;ul class="submenu"&gt;
                                                &lt;li&gt;&lt;a href="blog-right-sidebar.html"&gt;Blog Right Sidebar&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="blog-left-sidebar.html"&gt;Blog Left Sidebar&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="blog.html"&gt;Blog 3 Column&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="blog-details.html"&gt;Blog Details&lt;/a&gt;&lt;/li&gt;
                                            &lt;/ul&gt;
                                        &lt;/li&gt;
                                        &lt;li class="dropdown"&gt;&lt;a href="#"&gt;Pages&lt;/a&gt;
                                            &lt;ul class="submenu"&gt;
                                                &lt;li&gt;&lt;a href="about.html"&gt;About&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="appointment.html"&gt;Appointment&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="portfolio.html"&gt;Portfolio&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="portfolio-masonry.html"&gt;Portfolio Masonry&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="portfolio-details.html"&gt;Portfolio Details&lt;/a&gt;&lt;/li&gt;
                                                &lt;li&gt;&lt;a href="contact.html"&gt;Contact&lt;/a&gt;&lt;/li&gt;
                                            &lt;/ul&gt;
                                        &lt;/li&gt;
                                    &lt;/ul&gt;
                                &lt;/nav&gt;
                            &lt;/div&gt;
                            &lt;div class="mobile-menu"&gt;&lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;!-- info bar start --&gt;
                &lt;div class="btn-menu-main"&gt;
                    &lt;div class="crose mb-30"&gt;
                        &lt;button&gt;&lt;i class="fas fa-times"&gt;&lt;/i&gt;&lt;/button&gt;
                    &lt;/div&gt;
                    &lt;div class="logo-side mb-30"&gt;
                        &lt;a href="index.html"&gt;
                            &lt;img src="img/logo/logo.png" alt=""&gt;
                        &lt;/a&gt;
                    &lt;/div&gt;
                    &lt;div class="side-info mb-30"&gt;
                        &lt;div class="sidebar-content mb-45"&gt;
                            &lt;h4&gt;About us&lt;/h4&gt;
                            &lt;p&gt;Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&lt;/p&gt;
                            &lt;a class="thm-btn btn-icon" href="#" style="animation-delay: 0.6s;" tabindex="0"&gt;Contact us &lt;span&gt;&lt;i class="fas fa-arrow-right"&gt;&lt;/i&gt;&lt;/span&gt;&lt;/a&gt;
                        &lt;/div&gt;
                        &lt;div class="contact-list mb-30"&gt;
                            &lt;div class="sidebar-title"&gt;
                                &lt;h4&gt;Contact us&lt;/h4&gt;
                            &lt;/div&gt;
                            &lt;ul class="footer-info"&gt;
                                &lt;li&gt;
                                    &lt;div class="footer-address mt-20"&gt;
                                        &lt;span&gt;&lt;i class="fas fa-map-marker-alt"&gt;&lt;/i&gt;&lt;/span&gt;
                                        &lt;h5&gt;Bowery St., New York, NY 10013, USA&lt;/h5&gt;
                                    &lt;/div&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;div class="footer-address mt-20"&gt;
                                        &lt;span&gt;&lt;i class="fas fa-phone-alt"&gt;&lt;/i&gt;&lt;/span&gt;
                                        &lt;h5&gt;+1255 - 568 - 6523&lt;/h5&gt;
                                    &lt;/div&gt;
                                &lt;/li&gt;
                                &lt;li&gt;
                                    &lt;div class="footer-address mt-20"&gt;
                                        &lt;span&gt;&lt;i class="fas fa-envelope-open-text"&gt;&lt;/i&gt;&lt;/span&gt;
                                        &lt;h5&gt;<EMAIL>&lt;/h5&gt;
                                    &lt;/div&gt;
                                &lt;/li&gt;
                            &lt;/ul&gt;
                        &lt;/div&gt;
                        &lt;div class="sidebar-social mt-20"&gt;
                            &lt;a href="#"&gt;
                                &lt;i class="fab fa-facebook-f"&gt;&lt;/i&gt;
                            &lt;/a&gt;
                            &lt;a href="#"&gt;
                                &lt;i class="fab fa-twitter"&gt;&lt;/i&gt;
                            &lt;/a&gt;
                            &lt;a href="#"&gt;
                                &lt;i class="fab fa-google-plus-g"&gt;&lt;/i&gt;
                            &lt;/a&gt;
                            &lt;a href="#"&gt;
                                &lt;i class="fab fa-instagram"&gt;&lt;/i&gt;
                            &lt;/a&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
                &lt;div class="offcanvas-overly"&gt;&lt;/div&gt;
                &lt;!-- info bar end --&gt;
            &lt;/div&gt;
        &lt;/header&gt;
        &lt;!-- header end --&gt;
        

	&lt;!-- footer start --&gt;
        &lt;footer&gt;
            &lt;div class="footer-area footer-height pt-120 pb-80" data-background="img/bg/footer-bg.jpg"&gt;
                &lt;div class="container"&gt;
                    &lt;div class="row"&gt;
                        &lt;div class="col-xl-4 col-lg-4 col-md-6 pr-0"&gt;
                            &lt;div class="footer-widget footer-spache mb-40"&gt;
                                &lt;h3&gt;About us&lt;/h3&gt;
                                &lt;p&gt;Lorem ipsum dolor sit amet, consect etur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.&lt;/p&gt;
                                &lt;div class="footer-social mt-35"&gt;
                                    &lt;a href="#"&gt;&lt;i&gt; class="fab fa-twitter"&gt;&lt;/i&gt;&lt;/a&gt;
                                    &lt;a href="#"&gt;&lt;i&gt; class="fas fa-rss"&gt;&lt;/i&gt;&lt;/a&gt;
                                    &lt;a href="#"&gt;&lt;i&gt; class="fab fa-dribbble"&gt;&lt;/i&gt;&lt;/a&gt;
                                    &lt;a href="#"&gt;&lt;i&gt; class="fab fa-behance"&gt;&lt;/i&gt;&lt;/a&gt;
                                    &lt;a href="#"&gt;&lt;i&gt; class="fab fa-instagram"&gt;&lt;/i&gt;&lt;/a&gt;
                                &lt;/div&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-xl-2 col-lg-2 col-md-6 offset-xl-1"&gt;
                            &lt;div class="footer-widget mb-40"&gt;
                                &lt;h3&gt;Services&lt;/h3&gt;
                                &lt;ul&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Instant Medicine&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Kidney Solution&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Knee Surgery&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Eye Care&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Dental Care&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Body Surgery&lt;/a&gt;&lt;/li&gt;
                                &lt;/ul&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-xl-2 col-lg-2 col-md-6 pl-50"&gt;
                            &lt;div class="footer-widget footer-widget-2 mb-40"&gt;
                                &lt;h3&gt;Links&lt;/h3&gt;
                                &lt;ul&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Home&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;About&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Department&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Team&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Pricing&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;News&lt;/a&gt;&lt;/li&gt;
                                &lt;/ul&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-xl-3 col-lg-4 col-md-6"&gt;
                            &lt;div class="footer-widget mb-40"&gt;
                                &lt;h3&gt;Infomation&lt;/h3&gt;
                                &lt;p&gt;Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do&lt;/p&gt;
                                &lt;ul class="footer-info"&gt;
                                    &lt;li&gt;
                                        &lt;div class="footer-address mt-20"&gt;
                                            &lt;span&gt;&lt;i&gt; class="fas fa-map-marker-alt"&gt;&lt;/i&gt;&lt;/span&gt;
                                            &lt;h5&gt;Bowery St., New York, NY 10013, USA&lt;/h5&gt;
                                        &lt;/div&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt;
                                        &lt;div class="footer-address mt-20"&gt;
                                            &lt;span&gt;&lt;i&gt; class="fas fa-phone-alt"&gt;&lt;/i&gt;&lt;/span&gt;
                                            &lt;h5&gt;+1255 - 568 - 6523&lt;/h5&gt;
                                        &lt;/div&gt;
                                    &lt;/li&gt;
                                    &lt;li&gt;
                                        &lt;div class="footer-address mt-20"&gt;
                                            &lt;span&gt;&lt;i&gt; class="fas fa-envelope-open-text"&gt;&lt;/i&gt;&lt;/span&gt;
                                            &lt;h5&gt;<EMAIL>&lt;/h5&gt;
                                        &lt;/div&gt;
                                    &lt;/li&gt;
                                &lt;/ul&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
            &lt;div class="copyright-area pt-30 pb-30"&gt;
                &lt;div class="container"&gt;
                    &lt;div class="row align-items-center"&gt;
                        &lt;div class="col-xl-6 col-lg-6 col-md-6"&gt;
                            &lt;div class="copyright-text text-center text-md-left"&gt;
                                &lt;p&gt;	&copy; 2020 Medicat Designed by &lt;a&gt; href="https://themeforest.net/user/themerunch" target="_blank"&gt;themerunch&lt;/a&gt;&lt;/p&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                        &lt;div class="col-xl-6 col-lg-6 col-md-6"&gt;
                            &lt;div class="footer-menu text-center text-md-right"&gt;
                                &lt;ul&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Terms of Service&lt;/a&gt;&lt;/li&gt;
                                    &lt;li&gt;&lt;a&gt; href="#"&gt;Privacy Policy&lt;/a&gt;&lt;/li&gt;
                                &lt;/ul&gt;
                            &lt;/div&gt;
                        &lt;/div&gt;
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        &lt;/footer&gt;
        &lt;!-- footer end --&gt;
		
    
	&lt;!-- All js here --&gt;
	&lt;script src="js/vendor/modernizr-3.8.0.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/vendor/jquery-1.12.4.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/popper.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/bootstrap.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/owl.carousel.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/waypoints.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/jquery.counterup.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/isotope.pkgd.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/slick.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/jquery.scrollUp.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/jquery.meanmenu.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/jquery.nice-select.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/imagesloaded.pkgd.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/jquery.magnific-popup.min.js"&gt;&lt;/script&gt;
	&lt;script src="js/plugins.js"&gt;&lt;/script&gt;
	&lt;script src="js/main.js"&gt;&lt;/script&gt;
  &lt;/body&gt;
&lt;/html&gt;
						</div>
					</pre>
					<p>&nbsp;</p>
				</section>

				<section id="css_files_and_structure">
					<div class="page-header"><h3>CSS Structure</h3><hr class="notop"></div>
					<p>We are using several CSS files in this Template. themerunch Always follow the latest trends of coding standard. Many browser interpret the default behavior of HTML elements differently. By using a general reset CSS file, we can work round this. This file also contains some general styling, such as anchor tag colors, font-sizes, etc. Keep in mind, that these values might be overridden somewhere else in the file.</p>
					<p>CSS Fiile Included</p>
					<ul>
						<li>Animate css used</li>
						<li>Bootstrap css used</li>
						<li>Lightcase used</li>
						<li>Owl Carousel used</li>
						<li>slick used</li>
						<li>isotope used</li>
						<li>Favicon used</li>
						<li>Fontawesome used</li>
						<li>Style.css used</li>
						<li>and some other necessary css files&nbsp;</li>
					</ul>
					<div>
						<div>style.css situated on medicat/css/ contain the the all necessary custom style which is well organized and following are the style.css file structure :</div>
					</div>
					<div>&nbsp;</div>
					<div>&nbsp;</div>
	<pre class="prettyprint lang-css linenums">
	<div>&nbsp;</div>
	Project Name: Medicat - Medical and Health HTML5 Template 
	Author: themerunch -->> (https://themeforest.net/user/themerunch)
	Support: <EMAIL>
	Description: Medical and Health HTML5 Template 
	Developer: Wasim Mia
	Version: 1.0
	
	/* CSS Index
	-----------------------------------
	1. Theme default css
	2. header
	3. slider
	4. about
	5. servises
	6. cta
	7. team
	8. exprience
	9. pricing
	10. blog
	11. brand
	12. subscribe
	13. footer
	14. department
	15. counter
	16. appointment
	17. testimonial
	18. work
	19. portfolio
	20. faq
	21. page title
	22. shop
	23. contact
	24. login
	25. info bar
	26. Preloader
	*/
	<div>&nbsp;</div>
	</pre>

					<div>&nbsp;</div>
					<div>
						<div>&nbsp;</div>
	<pre class="prettyprint lang-css linenums">
	<div>&nbsp;</div>
	/* 2. header */
	.header-top{
		background-color: #1b1d21;
		padding: 13px 0px;
	}
	.header-top ul{
		overflow: hidden;
		margin: 0px;
	}
	.header-top .left {
		float: left;
	}
	.header-top .left li {
		list-style: none;
		display: inline-block;
		color: #fff;
		margin-right: 15px;
		float: left;
		border-left: 1px solid #393a3d;
		padding-left: 15px;
	}
	.header-top .left li:first-child {
		border: 0;
		padding-left: 0;
	}
	.header-top .left li span{
		color: #0a9aae;
		margin-right: 7px;
	}
	.header-top .right {
		float: right;
	}
	.header-top .right li {
		list-style: none;
		display: inline-block;
		padding: 0 11px;
	}
	.header-top .right li:last-child {
		padding-right: 0;
	}
	.header-top .right li a{
		color: #666666;
		font-size: 18px;
	}
	.header-top .right li a:hover{
		color: #0a9aae;
	}
	.main-menu ul li {
		display: inline-block;
		margin-left: 48px;
		position: relative;
	}
	.main-menu ul li a {
		display: block;
		padding: 40px 0;
		color: #1b1d21;
		font-size: 16px;
		font-weight: 700;
		position: relative;
	}
	.main-menu ul li.dropdown a::before {
		font-family: "Font Awesome 5 Free";
		content: "\f078";
		position: absolute;
		right: -14px;
		top: 0px;
		z-index: 5;
		font-weight: 900;
		top: 44px;
		font-size: 11px;
	}
	.main-menu ul li.dropdown ul.submenu li a::before  {
		display: none;
	}
	.main-menu ul li:hover > a {
		color: #0a9aae;
	}
	.main-menu ul li ul.submenu {
		position: absolute;
		top: 110%;
		left: 0;
		min-width: 210px;
		opacity: 0;
		visibility: hidden;
		background: #fff;
		padding: 15px 0;
		box-shadow: 0 0 10px 3px rgba(0, 0, 0, 0.05);
		transition: all 0.3s ease-out 0s;
		border-radius: 3px;
		z-index: 99;
		border-top: 3px solid #0a9aae;
	}
	.main-menu ul li:hover ul.submenu {
		top: 100%;
		opacity: 1;
		visibility: visible;
	}
	.main-menu ul li ul.submenu li {
		margin: 0;
		display: block;
	}
	.main-menu ul li ul.submenu li a {
		padding: 9px 25px;
		text-transform: capitalize;
	}
	.header-icon {
		margin-left: 44px;
	}
	.header-icon a {
		background: #0a9aae;
		width: 120px;
		height: 104px;
		display: block;
		text-align: center;
		font-size: 45px;
		color: #fff;
		line-height: 2.3;
	}
	.transparent-header {
		position: absolute;
		left: 0;
		right: 0;
		z-index: 1;
		margin-top: 20px;
	}
	.header-icon-2 a {
		border-radius: 10px;
		-webkit-border-radius: 10px;
		-moz-border-radius: 10px;
		-ms-border-radius: 10px;
		-o-border-radius: 10px;
	}
	/* 3. slider */
	.slider-height {
		min-height: 700px;
		background-position: center top;
		background-repeat: no-repeat;
	}
	.slider-active button.slick-arrow {
		position: absolute;
		top: 50%;
		left: 185px;
		transform: translateY(-50%);
		background: none;
		border: 0;
		font-size: 16px;
		padding: 0;
		color: #10111e;
		z-index: 2;
		opacity: 0;
		visibility: hidden;
		height: 70px;
		width: 70px;
		border-radius: 50%;
		cursor: pointer;
		background: #fff;
		line-height: 73px;
		transition: .3s;
	}
	.slider-active button.slick-next{left: auto;right: 185px;}
	.slider-active:hover button.slick-prev{left: 200px;}
	.slider-active:hover button.slick-next{right: 200px;}
	.slider-active:hover button{
		opacity: 1;
		visibility: visible;
	}
	.slider-active button:hover{
		background: #0a9aae;
		color: #fff;
		box-shadow: 0px 6px 12px 0px hsla(200, 95%, 50%, 0.357);
	}
	.silder-text .slider-caption h5 {
		color: #fff;
		text-transform: uppercase;
		margin-bottom: 20px;
		font-weight: 500;
	}
	.silder-text .slider-caption h1 {
		font-size: 60px;
		color: #fff;
		text-transform: capitalize;
		font-weight: 700;
	}
	.silder-text .slider-caption h1 {
		font-size: 60px;
		color: #fff;
		text-transform: capitalize;
		font-weight: 700;
		line-height: 1.2;
		margin-bottom: 25px;
	}
	.silder-text .slider-caption p {
		font-size: 18px;
		color: #fff;
		font-weight: 600;
		line-height: 30px;
	}
	.slider-btn a {
		margin-right: 25px;
		margin-top: 30px;
	}
	/* slider 2 */
	.slider-wrapper {
		background: #ffffff;
		padding: 45px 65px;
		padding-bottom: 94px;
		margin-top: 285px;
		box-shadow: 0px 30px 60px 0px rgba(10, 154, 174, 0.1);
		border-radius: 10px;
		margin-bottom: 80px;
	}
	.silder-text-2 .slider-caption h5 {
		color: #0a9aae;
	}
	.silder-text-2 .slider-caption h1 {
		color: #1b1d21;
	}
	.silder-text-2 .slider-caption p {
		color: #999999;
	}
	.slider-active-2 button.slick-arrow {
		top: 40%;
	}
	/* slider 3 */
	.slider-height-2 {
		min-height: 850px;
	}
	.slider-img {
		position: absolute;
		right: 10%;
		bottom: 0;
	}

	</pre>
					</div>
					<div>&nbsp;</div>
				</section>

				<section id="js_files_and_structure">
					<div class="page-header"><h3>Js Structure</h3><hr class="notop"></div>
					<p>We are using several Js files in this Template. themerunch Always follow the latest trends of coding standard. Many browser interpret the default behavior of HTML elements differently. By using a general reset Js file, we can work round this. This file also contains some general styling, such as anchor tag colors, font-sizes, etc. Keep in mind, that these values might be overridden somewhere else in the file.</p>
					<p>Js Fiile Included</p>
					<ul>
						<li>jquery.js used</li>
						<li>fontawesome.js used</li>
						<li>jquery-ui.js used</li>
						<li>bootstrap.min.js used</li>
						<li>isotope.js used</li>
						<li>scrollUp.min.js used</li>
						<li>owl.carousel.min.js used</li>
						<li>js/slick.min.js used</li>
						<li>counterup.min.js used</li>
						<li>active.js used</li>
						<li>and some other necessary css files&nbsp;</li>
					</ul>
					<div>
						<div>main.js situated on js/ contain the the all necessary custom style which is well organized and following are the main.js file structure :</div>
					</div>
	<pre class="prettyprint lang-css linenums">
	<div>&nbsp;</div>
	(function ($) {
		"use strict";
		
		// preloader
		$(window).on('load', function () {
			$('#preloader').delay(350).fadeOut('slow');
			$('body').delay(350).css({ 'overflow': 'visible' });
		})
	
		// side-bar
		$(".bar").on("click", function () {
			$(".btn-menu-main,.offcanvas-overly").addClass("btn-menu-main-right");
			return false;
		});
		$(".crose,.offcanvas-overly").on("click", function () {
			$(".btn-menu-main,.offcanvas-overly").removeClass("btn-menu-main-right");
		});
	
		// meanmenu
		jQuery('#mobile-menu').meanmenu({
			meanMenuContainer: '.mobile-menu',
			meanScreenWidth: "991"
		});
	
		//data background
		$("[data-background]").each(function () {
			$(this).css("background-image", "url(" + $(this).attr("data-background") + ") ")
		})
	
		//counterUp
		$('.counter').counterUp({
			delay: 10,
			time: 1000
		});
	
		// slider active
		function mainSlider() {
			var BasicSlider = $('.slider-active');
			BasicSlider.on('init', function(e, slick) {
				var $firstAnimatingElements = $('.single-slider:first-child').find('[data-animation]');
				doAnimations($firstAnimatingElements);
			});
			BasicSlider.on('beforeChange', function(e, slick, currentSlide, nextSlide) {
				var $animatingElements = $('.single-slider[data-slick-index="' + nextSlide + '"]').find('[data-animation]');
				doAnimations($animatingElements);
			});
			BasicSlider.slick({
				autoplay: false,
				autoplaySpeed: 10000,
				dots: false,
				fade: true,
				arrows: true,
				prevArrow: '<button type="button" class="slick-prev"><i class="fas fa-arrow-left"></i></button>',
				nextArrow: '<button type="button" class="slick-next"><i class="fas fa-arrow-right"></i></button>',
				responsive: [
					{ breakpoint: 767, settings: { dots: false, arrows: false } }
				]
			});
	
			function doAnimations(elements) {
				var animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend';
				elements.each(function() {
					var $this = $(this);
					var $animationDelay = $this.data('delay');
					var $animationType = 'animated ' + $this.data('animation');
					$this.css({
						'animation-delay': $animationDelay,
						'-webkit-animation-delay': $animationDelay
					});
					$this.addClass($animationType).one(animationEndEvents, function() {
						$this.removeClass($animationType);
					});
				});
			}
		}
		mainSlider();

		})(jQuery);


	<div>&nbsp;</div>
	</pre>
				<div>&nbsp;</div>
				</section>

				<section id="customization">
					<div class="page-header"><h3>Customization</h3><hr class="notop"></div>
					<h4>How to Change Logo</h4>
					<img src="assets/images/customization/logo.jpg" alt="">
					<h4>How to Change Copyright</h4>
					<img src="assets/images/customization/copyright.jpg" alt="">
					<h4>How to Change Google Map</h4>
					<img src="assets/images/customization/gmap.jpg" alt="">
				</section>

				<section id="credits">
					<div class="page-header"><h3>Credits</h3><hr class="notop"></div>
					<h5>jQuery</h5>
					<ul>
						<li><a href="http://jquery.com/" target="_blank">jQuery</a></li>
						<li><a href="http://fontawesome.com/" target="_blank">fontawesome</a></li>
						<li><a href="https://isotope.metafizzy.co/" target="_blank">OwlCarousel</a></li>
						<li><a href="http://kenwheeler.github.io/slick/" target="_blank">slick</a></li>
						<li><a href="http://cornel.bopp-art.com/lightcase/" target="_blank">Isotope</a></li>
						<li><a href="https://idangero.us/swiper/demos/" target="_blank">Counterup</a></li>
						<li><a href="https://developers.google.com/maps/" target="_blank">Google Map</a></li>
						<li><a href="#0" target="_blank">etc</a></li>
					</ul>
					<h5>CSS &amp; Fonts</h5>
					<ul>
						<li><a href="https://daneden.github.io/animate.css/" target="_blank">animate</a></li>
						<li><a href="http://getbootstrap.com/" target="_blank">bootstrap 4</a></li>
						<li><a href="https://www.google.com/fonts" target="_blank">google fonts</a></li>
						<li><a href="https://fonts.google.com/specimen/Poppins" target="_blank">Poppins</a></li>
						<li><a href="#0" target="_blank">etc&nbsp;</a></li>
					</ul>
				</section>
			</div>
		</div>
	</div>
</body>
</html>






