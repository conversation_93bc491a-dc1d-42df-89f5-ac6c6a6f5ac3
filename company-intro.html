<!doctype html>
<html class="no-js" lang="zxx">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="x-ua-compatible" content="ie=edge">
        <title>福祉华龄 - 科技焕活生命质量</title>
        <meta name="description" content="福祉华龄是一家由知名国际院士领衔，以科技焕活生命质量为核心理念，汇聚顶尖跨学科团队，致力于做最好的健康守护者">
        <meta name="viewport" content="width=device-width, initial-scale=1">

		<link rel="shortcut icon" type="image/x-icon" href="img/favicon.png">

        <!-- CSS here -->
        <link rel="stylesheet" href="css/bootstrap.min.css">
        <link rel="stylesheet" href="css/owl.carousel.min.css">
        <link rel="stylesheet" href="css/fontawesome-all.min.css">
        <link rel="stylesheet" href="css/animate.css">
        <link rel="stylesheet" href="css/magnific-popup.css">
        <link rel="stylesheet" href="css/meanmenu.css">
        <link rel="stylesheet" href="css/nice-select.css">
        <link rel="stylesheet" href="css/slick.css">
        <link rel="stylesheet" href="css/default.css">
        <link rel="stylesheet" href="css/style.css">
        <link rel="stylesheet" href="css/responsive.css">
        
        <!-- Custom styles for this page -->
        <style>
            .hero-section {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                position: relative;
                overflow: hidden;
            }
            
            .hero-section::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('img/bg/hero-pattern.png') repeat;
                opacity: 0.1;
                z-index: 1;
            }
            
            .hero-content {
                position: relative;
                z-index: 2;
                color: white;
                text-align: center;
            }
            
            .hero-title {
                font-size: 3.5rem;
                font-weight: 700;
                margin-bottom: 1.5rem;
                line-height: 1.2;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            
            .hero-subtitle {
                font-size: 1.5rem;
                margin-bottom: 2rem;
                opacity: 0.9;
                font-weight: 300;
            }
            
            .hero-description {
                font-size: 1.2rem;
                margin-bottom: 3rem;
                max-width: 800px;
                margin-left: auto;
                margin-right: auto;
                line-height: 1.6;
            }
            
            .floating-elements {
                position: absolute;
                width: 100%;
                height: 100%;
                overflow: hidden;
                z-index: 1;
            }
            
            .floating-element {
                position: absolute;
                background: rgba(255,255,255,0.1);
                border-radius: 50%;
                animation: float 6s ease-in-out infinite;
            }
            
            .floating-element:nth-child(1) {
                width: 80px;
                height: 80px;
                top: 20%;
                left: 10%;
                animation-delay: 0s;
            }
            
            .floating-element:nth-child(2) {
                width: 120px;
                height: 120px;
                top: 60%;
                right: 15%;
                animation-delay: 2s;
            }
            
            .floating-element:nth-child(3) {
                width: 60px;
                height: 60px;
                bottom: 30%;
                left: 20%;
                animation-delay: 4s;
            }
            
            @keyframes float {
                0%, 100% { transform: translateY(0px) rotate(0deg); }
                50% { transform: translateY(-20px) rotate(180deg); }
            }
            
            .mission-section {
                padding: 100px 0;
                background: #f8f9fa;
            }
            
            .mission-card {
                background: white;
                border-radius: 15px;
                padding: 40px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                margin-bottom: 30px;
                transition: transform 0.3s ease, box-shadow 0.3s ease;
                height: 100%;
            }
            
            .mission-card:hover {
                transform: translateY(-10px);
                box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            }
            
            .mission-icon {
                width: 80px;
                height: 80px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 25px;
                color: white;
                font-size: 2rem;
            }
            
            .mission-title {
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 15px;
                color: #333;
                text-align: center;
            }
            
            .mission-text {
                color: #666;
                line-height: 1.6;
                text-align: center;
            }
            
            .values-section {
                padding: 100px 0;
                background: white;
            }
            
            .section-title {
                text-align: center;
                margin-bottom: 60px;
            }
            
            .section-title h2 {
                font-size: 2.5rem;
                font-weight: 700;
                color: #333;
                margin-bottom: 15px;
            }
            
            .section-title p {
                font-size: 1.1rem;
                color: #666;
                max-width: 600px;
                margin: 0 auto;
            }
            
            .value-item {
                text-align: center;
                margin-bottom: 40px;
            }
            
            .value-number {
                width: 60px;
                height: 60px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 20px;
                color: white;
                font-size: 1.5rem;
                font-weight: 700;
            }
            
            .value-title {
                font-size: 1.3rem;
                font-weight: 600;
                margin-bottom: 10px;
                color: #333;
            }
            
            .value-text {
                color: #666;
                line-height: 1.6;
            }
            
            /* Responsive Design */
            @media (max-width: 768px) {
                .hero-title {
                    font-size: 2.5rem;
                }

                .hero-subtitle {
                    font-size: 1.2rem;
                }

                .hero-description {
                    font-size: 1rem;
                    padding: 0 20px;
                }

                .mission-card {
                    padding: 30px 20px;
                }

                .mission-section,
                .values-section {
                    padding: 60px 0;
                }

                .section-title h2 {
                    font-size: 2rem;
                }

                .floating-element {
                    display: none;
                }

                .leadership-card {
                    padding: 30px 20px !important;
                    margin-bottom: 20px !important;
                }

                .technology-section {
                    padding: 60px 0 !important;
                }

                .technology-content h3 {
                    font-size: 1.5rem !important;
                }

                .contact-section {
                    padding: 60px 0 !important;
                }

                .technology-image div {
                    width: 200px !important;
                    height: 200px !important;
                    font-size: 3rem !important;
                }
            }

            @media (max-width: 576px) {
                .hero-title {
                    font-size: 2rem;
                }

                .hero-subtitle {
                    font-size: 1.1rem;
                }

                .mission-card {
                    padding: 25px 15px;
                }

                .section-title h2 {
                    font-size: 1.8rem;
                }

                .leadership-card {
                    padding: 25px 15px !important;
                }

                .technology-content h3 {
                    font-size: 1.3rem !important;
                }

                .technology-image div {
                    width: 150px !important;
                    height: 150px !important;
                    font-size: 2.5rem !important;
                }

                .contact-item {
                    margin-bottom: 30px !important;
                }

                .contact-item div {
                    width: 60px !important;
                    height: 60px !important;
                    font-size: 1.5rem !important;
                }

                .contact-item h4 {
                    font-size: 1.1rem !important;
                }
            }

            /* Additional animations */
            .mission-card,
            .leadership-card {
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }

            .mission-card:hover,
            .leadership-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0,0,0,0.15);
            }

            /* Smooth scrolling */
            html {
                scroll-behavior: smooth;
            }

            /* Loading animation for icons */
            .mission-icon,
            .leadership-icon,
            .value-number {
                animation: iconPulse 2s ease-in-out infinite;
            }

            @keyframes iconPulse {
                0%, 100% { transform: scale(1); }
                50% { transform: scale(1.05); }
            }
        </style>
    </head>
    <body>

        <!-- preloader start -->
        <!-- <div id="preloader">
            <div class="preloader">
                <span></span>
                <span></span>
            </div>
        </div> -->
        <!-- preloader end  -->

        <!-- header start -->
        <!-- <header id="header-placeholder"></header> -->
        <!-- header end -->
        
        <main>
            <!-- Hero Section -->
            <section class="hero-section">
                <!-- <div class="floating-elements">
                    <div class="floating-element"></div>
                    <div class="floating-element"></div>
                    <div class="floating-element"></div>
                </div> -->
                <div class="container">
                    <div class="row">
                        <div class="col-12">
                            <div class="hero-content" data-aos="fade-up">
                                <h1 class="hero-title">福祉华龄</h1>
                                <p class="hero-subtitle">科技焕活生命质量</p>
                                <p class="hero-description">
                                    我们是一家由知名国际院士领衔的创新型医疗科技企业，以"科技焕活生命质量"为核心理念，
                                    汇聚顶尖跨学科团队，致力于成为最好的健康守护者，为人类健康事业贡献力量。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Mission Section -->
            

            <!-- Values Section -->
            

            <!-- Team Leadership Section -->
            <!-- <section class="team-leadership-section" style="padding: 100px 0; background: #f8f9fa;">
                <div class="container">
                    <div class="section-title">
                        <h2>领导团队</h2>
                        <p>由知名国际院士领衔的顶尖跨学科专家团队</p>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <div class="leadership-card" style="background: white; border-radius: 15px; padding: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
                                <div class="leadership-icon" style="width: 100px; height: 100px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 25px; color: white; font-size: 2.5rem;">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                                <h3 style="text-align: center; font-size: 1.5rem; font-weight: 600; margin-bottom: 15px; color: #333;">院士级专家团队</h3>
                                <p style="color: #666; line-height: 1.6; text-align: center;">
                                    由中国科学院院士和知名国际院士组成的顶级专家团队，在医学、生物工程、
                                    材料科学等领域拥有深厚的学术造诣和丰富的实践经验。
                                </p>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="leadership-card" style="background: white; border-radius: 15px; padding: 40px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); margin-bottom: 30px;">
                                <div class="leadership-icon" style="width: 100px; height: 100px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 25px; color: white; font-size: 2.5rem;">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <h3 style="text-align: center; font-size: 1.5rem; font-weight: 600; margin-bottom: 15px; color: #333;">跨学科研发团队</h3>
                                <p style="color: #666; line-height: 1.6; text-align: center;">
                                    汇聚医学、工程学、计算机科学、生物技术等多个领域的专业人才，
                                    形成强大的跨学科创新合力，推动医疗科技突破。
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </section> -->

            <!-- Technology Innovation Section -->
            <!-- <section class="technology-section" style="padding: 100px 0; background: white;">
                <div class="container">
                    <div class="section-title">
                        <h2>技术创新</h2>
                        <p>以科技创新为核心，打造世界领先的医疗健康解决方案</p>
                    </div>
                    <div class="row align-items-center">
                        <div class="col-lg-6">
                            <div class="technology-content">
                                <h3 style="font-size: 2rem; font-weight: 600; margin-bottom: 20px; color: #333;">前沿科技研发</h3>
                                <p style="color: #666; line-height: 1.8; margin-bottom: 25px;">
                                    我们专注于糖尿病医疗科技领域的创新研发，融合前沿物理疗法与智能交互技术，
                                    构建精准化血糖调控方案，为患者提供安全高效的非药物干预新路径。
                                </p>
                                <ul style="list-style: none; padding: 0;">
                                    <li style="margin-bottom: 15px; display: flex; align-items: center;">
                                        <i class="fas fa-check-circle" style="color: #667eea; margin-right: 10px; font-size: 1.2rem;"></i>
                                        <span>无创血糖调控技术</span>
                                    </li>
                                    <li style="margin-bottom: 15px; display: flex; align-items: center;">
                                        <i class="fas fa-check-circle" style="color: #667eea; margin-right: 10px; font-size: 1.2rem;"></i>
                                        <span>智能健康监测系统</span>
                                    </li>
                                    <li style="margin-bottom: 15px; display: flex; align-items: center;">
                                        <i class="fas fa-check-circle" style="color: #667eea; margin-right: 10px; font-size: 1.2rem;"></i>
                                        <span>个性化治疗方案</span>
                                    </li>
                                    <li style="margin-bottom: 15px; display: flex; align-items: center;">
                                        <i class="fas fa-check-circle" style="color: #667eea; margin-right: 10px; font-size: 1.2rem;"></i>
                                        <span>数据驱动的健康管理</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="technology-image" style="text-align: center;">
                                <div style="width: 300px; height: 300px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; color: white; font-size: 4rem;">
                                    <i class="fas fa-atom"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section> -->
        </main>

        <!-- footer start -->
        <!-- <footer id="footer-placeholder"></footer> -->
        <!-- footer end -->

        <!-- JS here -->
        <script src="js/vendor/modernizr-3.8.0.min.js"></script>
        <script src="js/vendor/jquery-1.12.4.min.js"></script>
        <script src="js/popper.min.js"></script>
        <script src="js/bootstrap.min.js"></script>
        <script src="js/owl.carousel.min.js"></script>
        <script src="js/waypoints.min.js"></script>
        <script src="js/jquery.counterup.min.js"></script>
        <script src="js/isotope.pkgd.min.js"></script>
        <script src="js/slick.min.js"></script>
        <script src="js/jquery.scrollUp.min.js"></script>
        <script src="js/jquery.meanmenu.min.js"></script>
        <script src="js/jquery.nice-select.min.js"></script>
        <script src="js/imagesloaded.pkgd.min.js"></script>
        <script src="js/jquery.magnific-popup.min.js"></script>
        <script src="js/plugins.js"></script>
        <script src="js/main.js"></script>
        
        <!-- Load header and footer -->
        <script>
            $(document).ready(function() {
                $('#header-placeholder').load('inc-header.html');
                $('#footer-placeholder').load('inc-footer.html');
            });
        </script>
    </body>
</html>
